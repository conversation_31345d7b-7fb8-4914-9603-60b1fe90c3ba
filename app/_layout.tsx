import {
  DarkTheme,
  De<PERSON>ultTheme,
  ThemeProvider,
} from "@react-navigation/native";
import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import { StatusBar } from "expo-status-bar";
import "react-native-reanimated";
import { useEffect, useState } from "react"; // Import useEffect and useState
import AsyncStorage from "@react-native-async-storage/async-storage"; // Import AsyncStorage
import { Appearance, I18nManager, ColorSchemeName } from "react-native"; // Import Appearance and ColorSchemeName
import { PaperProvider } from 'react-native-paper';

import { useColorScheme } from "@/hooks/useColorScheme"; // Keep this for system preference fallback
import { lightTheme, darkTheme } from "@/constants/PaperTheme";

export default function RootLayout() {
  const [appColorScheme, setAppColorScheme] = useState<ColorSchemeName | null>(null);
  const [loaded] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
    Vazirmatn: require("../assets/fonts/vazirmatn/Vazirmatn-Regular.ttf"),
  });

  // Enable RTL layout
  I18nManager.allowRTL(true);
  I18nManager.forceRTL(true);

  useEffect(() => {
    const loadInitialColorScheme = async () => {
      try {
        const storedDarkMode = await AsyncStorage.getItem('darkMode');
        if (storedDarkMode !== null) {
          const isDarkMode = JSON.parse(storedDarkMode);
          setAppColorScheme(isDarkMode ? "dark" : "light");
        } else {
          // If no preference is stored, use the system's current color scheme
          setAppColorScheme(Appearance.getColorScheme() ?? "light");
        }
      } catch (e) {
        console.error("Failed to load initial color scheme from AsyncStorage", e);
        setAppColorScheme(Appearance.getColorScheme() ?? "light"); // Fallback to system
      }
    };

    loadInitialColorScheme();

    // Listen for system color scheme changes (though our app will override with persisted setting)
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      // This listener is primarily for reacting to system changes,
      // but our app's theme is controlled by AsyncStorage.
      // We might re-evaluate this if we want to sync with system changes when no preference is set.
      // For now, we rely on the persisted state.
    });

    return () => subscription.remove();
  }, []);

  if (!loaded || appColorScheme === null) {
    // Async font loading only occurs in development.
    // Also wait until the color scheme is loaded from AsyncStorage
    return null;
  }

  const paperTheme = appColorScheme === "dark" ? darkTheme : lightTheme;

  return (
    <PaperProvider theme={paperTheme}>
      <ThemeProvider value={appColorScheme === "dark" ? DarkTheme : DefaultTheme}>
        <Stack>
          {/* <Stack.Screen name="about" options={{ headerShown: false }} /> */}
          <Stack.Screen name="(drawer)" options={{ headerShown: false }} />
          {/* <Stack.Screen name="(tabs)" options={{ headerShown: false }} /> */}
          {/* <Stack.Screen name="MajorDetails" options={{ headerShown: false }} />
          <Stack.Screen name="MajorAdd" options={{ headerShown: false }} /> */}
          {/* <Stack.Screen
            name="screens/MajorsList"
            options={{ title: "", headerShown: true }}
          /> */}
          {/* <Stack.Screen name="MajorEdit" options={{ headerShown: false }} />
          <Stack.Screen name="CourseDetails" options={{ headerShown: false }} />
          <Stack.Screen name="CourseAdd" options={{ headerShown: false }} />
          <Stack.Screen name="CourseEdit" options={{ headerShown: false }} />
          <Stack.Screen name="ChapterDetails" options={{ headerShown: false }} />
          <Stack.Screen name="ChapterAdd" options={{ headerShown: false }} />
          <Stack.Screen name="ChapterEdit" options={{ headerShown: false }} /> */}
          {/* <Stack.Screen
            name="MajorCourseDetails"
            options={{ headerShown: false }}
          /> */}
          {/* <Stack.Screen name="MajorCourseAdd" options={{ headerShown: false }} />
          <Stack.Screen name="MajorCourseEdit" options={{ headerShown: false }} />
          <Stack.Screen name="QuestionDetails" options={{ headerShown: false }} />
          <Stack.Screen name="QuestionAdd" options={{ headerShown: false }} />
          <Stack.Screen name="QuestionEdit" options={{ headerShown: false }} />
          <Stack.Screen name="OptionDetails" options={{ headerShown: false }} />
          <Stack.Screen name="OptionAdd" options={{ headerShown: false }} />
          <Stack.Screen name="OptionEdit" options={{ headerShown: false }} />
          <Stack.Screen name="UserDetails" options={{ headerShown: false }} />
          <Stack.Screen name="UserAdd" options={{ headerShown: false }} />
          <Stack.Screen name="UserEdit" options={{ headerShown: false }} />
          <Stack.Screen name="SessionDetails" options={{ headerShown: false }} />
          <Stack.Screen name="SessionAdd" options={{ headerShown: false }} />
          <Stack.Screen name="SessionEdit" options={{ headerShown: false }} /> */}
          {/* <Stack.Screen name="+not-found" /> */}
        </Stack>
        <StatusBar style="auto" />
      </ThemeProvider>
    </PaperProvider>
  );
}
