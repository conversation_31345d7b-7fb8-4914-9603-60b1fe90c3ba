import React, { useEffect, useState } from "react";
import { StyleSheet, SafeAreaView } from "react-native";
import { 
  Appbar, 
  Surface, 
  useTheme,
} from "react-native-paper";
import { router } from "expo-router";

// Paper components
import {
  PaperRefreshableFlatList,
  PaperCard,
  PaperCardTitle,
  PaperCardActions,
  PaperButton,
  PaperTitle,
  PaperAddFAB,
  PaperDeleteDialog,
  useRefresh,
} from "@/components/paper";

// Services and types
import { Major, majorService } from "@/services/majorService";
import { t } from "@/constants/Localization";

export default function MajorsListPaperScreen() {
  const [majors, setMajors] = useState<Major[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogVisible, setDeleteDialogVisible] = useState(false);
  const [selectedMajor, setSelectedMajor] = useState<Major | null>(null);
  
  const theme = useTheme();

  const loadMajors = async () => {
    try {
      setError(null);
      const data = await majorService.getMajors();
      setMajors(data);
    } catch (err) {
      setError(t('networkError') || "خطا در دریافت رشته‌ها.");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const { refreshing, onRefresh } = useRefresh(loadMajors);

  useEffect(() => {
    loadMajors();
  }, []);

  const handleBack = () => {
    router.back();
  };

  const handleAddMajor = () => {
    router.push("/screens/MajorAdd");
  };

  const handleMajorPress = (major: Major) => {
    router.push(`/screens/MajorDetails?id=${major.id}`);
  };

  const handleEditMajor = (major: Major) => {
    router.push(`/screens/MajorEdit?id=${major.id}`);
  };

  const handleDeleteMajor = (major: Major) => {
    setSelectedMajor(major);
    setDeleteDialogVisible(true);
  };

  const confirmDelete = async () => {
    if (!selectedMajor) return;
    
    try {
      await majorService.deleteMajor(selectedMajor.id);
      setMajors(prev => prev.filter(m => m.id !== selectedMajor.id));
      // Show success message
    } catch (err) {
      console.error('Delete error:', err);
      // Show error message
    }
  };

  const renderMajorItem = ({ item }: { item: Major }) => (
    <PaperCard
      style={styles.majorCard}
      onPress={() => handleMajorPress(item)}
    >
      <PaperCardTitle
        title={item.name}
        subtitle={item.description || t('majorDescription')}
        left={(props) => (
          <Surface {...props} style={styles.majorIcon}>
            <PaperTitle size="small">{item.name.charAt(0)}</PaperTitle>
          </Surface>
        )}
      />
      <PaperCardActions>
        <PaperButton
          title={t('edit')}
          mode="outlined"
          onPress={() => handleEditMajor(item)}
          icon="pencil"
          compact
        />
        <PaperButton
          title={t('delete')}
          mode="outlined"
          onPress={() => handleDeleteMajor(item)}
          icon="delete"
          compact
          style={styles.deleteButton}
        />
      </PaperCardActions>
    </PaperCard>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <Appbar.Header>
        <Appbar.BackAction onPress={handleBack} />
        <Appbar.Content 
          title={t('majorsList')} 
          titleStyle={styles.appBarTitle}
        />
        <Appbar.Action 
          icon="plus" 
          onPress={handleAddMajor}
        />
      </Appbar.Header>

      {/* Content */}
      <Surface style={styles.content}>
        <PaperRefreshableFlatList
          data={majors}
          renderItem={renderMajorItem}
          keyExtractor={(item) => item.id}
          refreshing={refreshing}
          onRefresh={onRefresh}
          loading={loading}
          style={styles.list}
          contentContainerStyle={styles.listContent}
          emptyTitle={t('noMajorsFound') || 'رشته‌ای یافت نشد'}
          emptyDescription={t('noMajorsDescription') || 'برای افزودن رشته جدید از دکمه + استفاده کنید'}
          emptyIcon="school"
          emptyAction={
            <PaperButton
              title={t('addMajor')}
              mode="contained"
              onPress={handleAddMajor}
              icon="plus"
            />
          }
        />
      </Surface>

      {/* Floating Action Button */}
      <PaperAddFAB
        onPress={handleAddMajor}
        visible={!loading}
      />

      {/* Delete Confirmation Dialog */}
      <PaperDeleteDialog
        visible={deleteDialogVisible}
        onDismiss={() => setDeleteDialogVisible(false)}
        onConfirm={confirmDelete}
        itemName={selectedMajor?.name}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  appBarTitle: {
    fontFamily: 'Vazirmatn',
    textAlign: 'right',
  },
  content: {
    flex: 1,
  },
  list: {
    flex: 1,
  },
  listContent: {
    padding: 8,
  },
  majorCard: {
    marginVertical: 4,
    marginHorizontal: 8,
  },
  majorIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  deleteButton: {
    marginLeft: 8,
  },
});
