import React from 'react';
import { t } from '@/constants/Localization';

// Toast types
export enum ToastType {
  SUCCESS = 'SUCCESS',
  ERROR = 'ERROR',
  WARNING = 'WARNING',
  INFO = 'INFO',
}

// Toast configuration
export interface ToastConfig {
  message: string;
  type: ToastType;
  duration?: number;
  action?: {
    label: string;
    onPress: () => void;
  };
  onDismiss?: () => void;
}

// Toast state for the provider
export interface ToastState {
  visible: boolean;
  config: ToastConfig | null;
}

// Toast context type
export interface ToastContextType {
  showToast: (config: ToastConfig) => void;
  hideToast: () => void;
  showSuccess: (message: string, action?: ToastConfig['action']) => void;
  showError: (message: string, action?: ToastConfig['action']) => void;
  showWarning: (message: string, action?: ToastConfig['action']) => void;
  showInfo: (message: string, action?: ToastConfig['action']) => void;
  showRetryError: (message: string, onRetry: () => void) => void;
}

// Default durations for different toast types (in milliseconds)
const DEFAULT_DURATIONS = {
  [ToastType.SUCCESS]: 3000,
  [ToastType.ERROR]: 5000,
  [ToastType.WARNING]: 4000,
  [ToastType.INFO]: 3000,
};

// Create the toast context
export const ToastContext = React.createContext<ToastContextType | null>(null);

// Custom hook to use toast
export const useToast = (): ToastContextType => {
  const context = React.useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

// Toast provider component
export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [toastState, setToastState] = React.useState<ToastState>({
    visible: false,
    config: null,
  });

  const showToast = React.useCallback((config: ToastConfig) => {
    setToastState({
      visible: true,
      config: {
        ...config,
        duration: config.duration || DEFAULT_DURATIONS[config.type],
      },
    });
  }, []);

  const hideToast = React.useCallback(() => {
    setToastState(prev => ({
      ...prev,
      visible: false,
    }));
  }, []);

  const showSuccess = React.useCallback((message: string, action?: ToastConfig['action']) => {
    showToast({
      message,
      type: ToastType.SUCCESS,
      action,
    });
  }, [showToast]);

  const showError = React.useCallback((message: string, action?: ToastConfig['action']) => {
    showToast({
      message,
      type: ToastType.ERROR,
      action,
    });
  }, [showToast]);

  const showWarning = React.useCallback((message: string, action?: ToastConfig['action']) => {
    showToast({
      message,
      type: ToastType.WARNING,
      action,
    });
  }, [showToast]);

  const showInfo = React.useCallback((message: string, action?: ToastConfig['action']) => {
    showToast({
      message,
      type: ToastType.INFO,
      action,
    });
  }, [showToast]);

  const showRetryError = React.useCallback((message: string, onRetry: () => void) => {
    showToast({
      message,
      type: ToastType.ERROR,
      action: {
        label: t('retry'),
        onPress: onRetry,
      },
    });
  }, [showToast]);

  const contextValue: ToastContextType = {
    showToast,
    hideToast,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showRetryError,
  };

  return (
    <ToastContext.Provider value={contextValue}>
      {children}
      <ToastRenderer toastState={toastState} onDismiss={hideToast} />
    </ToastContext.Provider>
  );
};

// Toast renderer component
interface ToastRendererProps {
  toastState: ToastState;
  onDismiss: () => void;
}

const ToastRenderer: React.FC<ToastRendererProps> = ({ toastState, onDismiss }) => {
  const { Snackbar } = require('react-native-paper');
  
  if (!toastState.config) {
    return null;
  }

  const { message, type, action, duration, onDismiss: configOnDismiss } = toastState.config;

  const handleDismiss = () => {
    onDismiss();
    configOnDismiss?.();
  };

  // Get theme colors based on toast type
  const getToastStyle = (toastType: ToastType) => {
    switch (toastType) {
      case ToastType.SUCCESS:
        return {
          backgroundColor: '#4CAF50',
          color: '#FFFFFF',
        };
      case ToastType.ERROR:
        return {
          backgroundColor: '#F44336',
          color: '#FFFFFF',
        };
      case ToastType.WARNING:
        return {
          backgroundColor: '#FF9800',
          color: '#FFFFFF',
        };
      case ToastType.INFO:
        return {
          backgroundColor: '#2196F3',
          color: '#FFFFFF',
        };
      default:
        return {};
    }
  };

  const toastStyle = getToastStyle(type);

  return (
    <Snackbar
      visible={toastState.visible}
      onDismiss={handleDismiss}
      duration={duration}
      action={action ? {
        label: action.label,
        onPress: () => {
          action.onPress();
          handleDismiss();
        },
        textColor: toastStyle.color,
      } : undefined}
      style={{
        backgroundColor: toastStyle.backgroundColor,
      }}
      theme={{
        colors: {
          onSurface: toastStyle.color || '#FFFFFF',
        },
      }}
    >
      {message}
    </Snackbar>
  );
};

// Utility functions for common toast scenarios
export const ToastUtils = {
  // Network error with retry
  networkError: (onRetry?: () => void) => {
    const toast = useToast();
    if (onRetry) {
      toast.showRetryError(t('connectionError'), onRetry);
    } else {
      toast.showError(t('connectionError'));
    }
  },

  // Server error
  serverError: () => {
    const toast = useToast();
    toast.showError(t('serverError'));
  },

  // Success operations
  saveSuccess: () => {
    const toast = useToast();
    toast.showSuccess(t('saveSuccess'));
  },

  deleteSuccess: () => {
    const toast = useToast();
    toast.showSuccess(t('deleteSuccess'));
  },

  updateSuccess: () => {
    const toast = useToast();
    toast.showSuccess(t('updateSuccess'));
  },

  // Load failures with retry
  loadFailed: (onRetry?: () => void) => {
    const toast = useToast();
    if (onRetry) {
      toast.showRetryError(t('dataLoadFailed'), onRetry);
    } else {
      toast.showError(t('dataLoadFailed'));
    }
  },
};
