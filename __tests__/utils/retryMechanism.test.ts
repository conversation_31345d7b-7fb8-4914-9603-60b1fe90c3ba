import { 
  withRetry, 
  createRetryableFunction, 
  RetryPresets, 
  RetryManager,
  DEFAULT_RETRY_CONFIG 
} from '../../utils/retryMechanism';

describe('Retry Mechanism', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock setTimeout for faster tests
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('withRetry', () => {
    it('should succeed on first attempt', async () => {
      const mockOperation = jest.fn().mockResolvedValue('success');
      
      const promise = withRetry(mockOperation);
      
      // Fast-forward timers to resolve any pending promises
      jest.runAllTimers();
      
      const result = await promise;
      
      expect(result.success).toBe(true);
      expect(result.data).toBe('success');
      expect(result.attempts).toBe(1);
      expect(mockOperation).toHaveBeenCalledTimes(1);
    });

    it('should retry on failure and eventually succeed', async () => {
      const mockOperation = jest.fn()
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValue('success');
      
      const promise = withRetry(mockOperation, { maxAttempts: 3 });
      
      // Fast-forward timers to handle delays
      jest.runAllTimers();
      
      const result = await promise;
      
      expect(result.success).toBe(true);
      expect(result.data).toBe('success');
      expect(result.attempts).toBe(3);
      expect(mockOperation).toHaveBeenCalledTimes(3);
    });

    it('should fail after max attempts', async () => {
      const mockError = new Error('Persistent error');
      const mockOperation = jest.fn().mockRejectedValue(mockError);
      
      const promise = withRetry(mockOperation, { maxAttempts: 2 });
      
      // Fast-forward timers
      jest.runAllTimers();
      
      const result = await promise;
      
      expect(result.success).toBe(false);
      expect(result.error).toBe(mockError);
      expect(result.attempts).toBe(2);
      expect(mockOperation).toHaveBeenCalledTimes(2);
    });

    it('should respect custom retry condition', async () => {
      const mockError = new Error('Non-retryable error');
      const mockOperation = jest.fn().mockRejectedValue(mockError);
      const mockRetryCondition = jest.fn().mockReturnValue(false);
      
      const promise = withRetry(mockOperation, { 
        maxAttempts: 3,
        retryCondition: mockRetryCondition 
      });
      
      jest.runAllTimers();
      
      const result = await promise;
      
      expect(result.success).toBe(false);
      expect(result.attempts).toBe(1);
      expect(mockOperation).toHaveBeenCalledTimes(1);
      expect(mockRetryCondition).toHaveBeenCalledWith(mockError, 1);
    });

    it('should call onRetry callback', async () => {
      const mockError = new Error('Network error');
      const mockOperation = jest.fn()
        .mockRejectedValueOnce(mockError)
        .mockResolvedValue('success');
      const mockOnRetry = jest.fn();
      
      const promise = withRetry(mockOperation, { 
        maxAttempts: 2,
        onRetry: mockOnRetry 
      });
      
      jest.runAllTimers();
      
      await promise;
      
      expect(mockOnRetry).toHaveBeenCalledWith(mockError, 1);
    });

    it('should call onMaxRetriesReached callback', async () => {
      const mockError = new Error('Persistent error');
      const mockOperation = jest.fn().mockRejectedValue(mockError);
      const mockOnMaxRetriesReached = jest.fn();
      
      const promise = withRetry(mockOperation, { 
        maxAttempts: 2,
        onMaxRetriesReached: mockOnMaxRetriesReached 
      });
      
      jest.runAllTimers();
      
      await promise;
      
      expect(mockOnMaxRetriesReached).toHaveBeenCalledWith(mockError);
    });
  });

  describe('createRetryableFunction', () => {
    it('should create a retryable version of a function', async () => {
      const originalFunction = jest.fn()
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValue('success');
      
      const retryableFunction = createRetryableFunction(originalFunction, { maxAttempts: 2 });
      
      const promise = retryableFunction('arg1', 'arg2');
      jest.runAllTimers();
      
      const result = await promise;
      
      expect(result).toBe('success');
      expect(originalFunction).toHaveBeenCalledTimes(2);
      expect(originalFunction).toHaveBeenCalledWith('arg1', 'arg2');
    });

    it('should throw error if all retries fail', async () => {
      const mockError = new Error('Persistent error');
      const originalFunction = jest.fn().mockRejectedValue(mockError);
      
      const retryableFunction = createRetryableFunction(originalFunction, { maxAttempts: 2 });
      
      const promise = retryableFunction();
      jest.runAllTimers();
      
      await expect(promise).rejects.toThrow('Persistent error');
    });
  });

  describe('RetryPresets', () => {
    it('should have correct QUICK preset', () => {
      expect(RetryPresets.QUICK).toEqual({
        maxAttempts: 2,
        baseDelay: 500,
        maxDelay: 2000,
        backoffFactor: 1.5,
      });
    });

    it('should have correct STANDARD preset', () => {
      expect(RetryPresets.STANDARD).toEqual({
        maxAttempts: 3,
        baseDelay: 1000,
        maxDelay: 5000,
        backoffFactor: 2,
      });
    });

    it('should have correct AGGRESSIVE preset', () => {
      expect(RetryPresets.AGGRESSIVE).toEqual({
        maxAttempts: 5,
        baseDelay: 1000,
        maxDelay: 15000,
        backoffFactor: 2,
      });
    });
  });

  describe('RetryManager', () => {
    let retryManager: RetryManager;

    beforeEach(() => {
      retryManager = new RetryManager();
    });

    it('should execute operation with retry', async () => {
      const mockOperation = jest.fn()
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValue('success');
      
      const promise = retryManager.executeWithRetry('test-key', mockOperation, { maxAttempts: 2 });
      jest.runAllTimers();
      
      const result = await promise;
      
      expect(result.success).toBe(true);
      expect(result.data).toBe('success');
    });

    it('should cancel retry operation', async () => {
      const mockOperation = jest.fn().mockImplementation(() => 
        new Promise((resolve, reject) => {
          setTimeout(() => reject(new Error('Network error')), 1000);
        })
      );
      
      const promise = retryManager.executeWithRetry('test-key', mockOperation);
      
      // Cancel the retry
      retryManager.cancelRetry('test-key');
      
      jest.runAllTimers();
      
      // The operation should still complete, but the retry should be cancelled
      await expect(promise).resolves.toBeDefined();
    });

    it('should track active retries', () => {
      const mockOperation = jest.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(resolve, 1000))
      );
      
      retryManager.executeWithRetry('test-key-1', mockOperation);
      retryManager.executeWithRetry('test-key-2', mockOperation);
      
      expect(retryManager.getActiveRetryCount()).toBe(2);
      expect(retryManager.isRetryActive('test-key-1')).toBe(true);
      expect(retryManager.isRetryActive('test-key-3')).toBe(false);
    });

    it('should cancel all retries', () => {
      const mockOperation = jest.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(resolve, 1000))
      );
      
      retryManager.executeWithRetry('test-key-1', mockOperation);
      retryManager.executeWithRetry('test-key-2', mockOperation);
      
      expect(retryManager.getActiveRetryCount()).toBe(2);
      
      retryManager.cancelAllRetries();
      
      expect(retryManager.getActiveRetryCount()).toBe(0);
    });
  });

  describe('Exponential backoff', () => {
    it('should increase delay exponentially', async () => {
      const mockOperation = jest.fn()
        .mockRejectedValueOnce(new Error('Error 1'))
        .mockRejectedValueOnce(new Error('Error 2'))
        .mockResolvedValue('success');
      
      const startTime = Date.now();
      
      const promise = withRetry(mockOperation, {
        maxAttempts: 3,
        baseDelay: 100,
        backoffFactor: 2,
      });
      
      // Manually advance timers to simulate delays
      jest.advanceTimersByTime(100); // First retry delay
      jest.advanceTimersByTime(200); // Second retry delay (100 * 2)
      
      const result = await promise;
      
      expect(result.success).toBe(true);
      expect(mockOperation).toHaveBeenCalledTimes(3);
    });
  });
});
